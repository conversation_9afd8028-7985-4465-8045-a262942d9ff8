<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
    
</body>
<script>
    const appId = '6448311069'; // 替换为实际的App ID
 
const xhr = new XMLHttpRequest();
xhr.open('GET', `https://itunes.apple.com/lookup?id=${appId}`, true);
xhr.onreadystatechange = function () {
    if (xhr.readyState === 4 && xhr.status === 200) {
        const data = JSON.parse(xhr.responseText);
        console.log(data);
    }
};
xhr.send();
</script>
</html>